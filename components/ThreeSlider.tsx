import React, { useEffect, useRef, useState } from 'react';
import { Dimensions, Image, NativeScrollEvent, NativeSyntheticEvent, Pressable, ScrollView, StyleSheet, Text, View } from 'react-native';

const SLIDES = [
  {
    title: 'Discover Opportunities',
    description: 'Explore bootcamps and courses tailored to your entrepreneurial journey.',
    bg: ['#6366F1', '#818CF8'],
    cta: 'Explore Bootcamps',
    image: require('../assets/images/partial-react-logo.png'),
  },
  {
    title: 'Learn from Experts',
    description: 'Gain insights and skills from experienced mentors and industry leaders.',
    bg: ['#F59E42', '#FBBF24'],
    cta: 'Meet Mentors',
    image: require('../assets/images/react-logo.png'),
  },
  {
    title: 'Join a Community',
    description: 'Connect with like-minded peers and grow your network.',
    bg: ['#10B981', '#34D399'],
    cta: 'Join Now',
    image: require('../assets/images/splash-icon.png'),
  },
];

function getResponsiveStyles(windowWidth: number) {
  if (windowWidth < 500) {
    // Mobile
    return {
      slideWidth: Math.max(windowWidth * 0.7, 220), // narrower
      minHeight: 400, // taller
      maxHeight: 480, // taller
      padding: 16,
      imageSize: 64,
      titleSize: 20,
      descSize: 14,
    };
  } else if (windowWidth < 900) {
    // Tablet
    return {
      slideWidth: Math.max(windowWidth * 0.5, 260), // narrower
      minHeight: 480, // taller
      maxHeight: 560, // taller
      padding: 24,
      imageSize: 90,
      titleSize: 24,
      descSize: 16,
    };
  } else {
    // Desktop
    return {
      slideWidth: 320, // fixed, narrow
      minHeight: 520, // taller
      maxHeight: 600, // taller
      padding: 36,
      imageSize: 120,
      titleSize: 28,
      descSize: 18,
    };
  }
}

export default function ThreeSlider() {
  const windowWidth = Dimensions.get('window').width;
  const {
    slideWidth,
    minHeight,
    maxHeight,
    padding,
    imageSize,
    titleSize,
    descSize,
  } = getResponsiveStyles(windowWidth);

  // For vertical slider, use slideHeight
  const slideHeight = maxHeight;

  const scrollRef = useRef<ScrollView>(null);
  const [active, setActive] = useState(0);
  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);

  // Auto-scroll logic (vertical)
  useEffect(() => {
    if (timerRef.current) clearInterval(timerRef.current);
    timerRef.current = setInterval(() => {
      setActive((prev) => {
        const next = prev === SLIDES.length - 1 ? 0 : prev + 1;
        if (scrollRef.current) {
          scrollRef.current.scrollTo({ y: next * slideHeight, animated: true });
        }
        return next;
      });
    }, 4000);
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, [slideHeight]);

  // Reset timer on user interaction
  const resetTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = setInterval(() => {
        setActive((prev) => {
          const next = prev === SLIDES.length - 1 ? 0 : prev + 1;
          if (scrollRef.current) {
            scrollRef.current.scrollTo({ y: next * slideHeight, animated: true });
          }
          return next;
        });
      }, 4000);
    }
  };

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const y = event.nativeEvent.contentOffset.y;
    const index = Math.round(y / slideHeight);
    setActive(index);
    resetTimer();
  };

  const goToSlide = (index: number) => {
    if (scrollRef.current) {
      scrollRef.current.scrollTo({ y: index * slideHeight, animated: true });
      setActive(index);
      resetTimer();
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.sliderCol}>
        <View style={{ height: slideHeight, width: slideWidth, alignSelf: 'center' }}>
          <ScrollView
            ref={scrollRef}
            pagingEnabled
            snapToInterval={slideHeight}
            decelerationRate="fast"
            showsVerticalScrollIndicator={false}
            onScroll={handleScroll}
            scrollEventThrottle={16}
            style={{ height: slideHeight, width: slideWidth }}
            contentContainerStyle={{ alignItems: 'center', justifyContent: 'center' }}
          >
            {SLIDES.map((slide, idx) => (
              <View key={idx} style={[styles.slide, { height: slideHeight, width: slideWidth, backgroundColor: slide.bg[0], shadowColor: slide.bg[0], minHeight, maxHeight }]}> 
                <View style={[styles.slideContent, { padding }]}> 
                  <Image source={slide.image} style={[styles.slideImage, { width: imageSize, height: imageSize }]} resizeMode="contain" />
                  <Text style={[styles.slideTitle, { fontSize: titleSize }]}>{slide.title}</Text>
                  <Text style={[styles.slideDesc, { fontSize: descSize }]}>{slide.description}</Text>
                  <Pressable style={styles.slideCtaButton}>
                    <Text style={styles.slideCtaText}>{slide.cta}</Text>
                  </Pressable>
                </View>
              </View>
            ))}
          </ScrollView>
        </View>
      </View>
      <View style={styles.dotsCol}>
        {SLIDES.map((_, idx) => (
          <Pressable
            key={idx}
            style={[styles.dot, active === idx && styles.dotActive]}
            onPress={() => goToSlide(idx)}
          />
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row', // Main axis is horizontal: slider + dots
    alignItems: 'center',
    marginTop: 0,
    marginBottom: 48,
    width: '100%',
    justifyContent: 'center',
  },
  sliderCol: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  slide: {
    marginHorizontal: 0,
    padding: 0,
    alignItems: 'center',
    justifyContent: 'center',
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 4,
    overflow: 'hidden',
    display: 'flex',
    alignSelf: 'center',
  },
  slideContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  slideImage: {
    marginBottom: 24,
    borderRadius: 24,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
  slideTitle: {
    fontWeight: '900',
    color: '#fff',
    marginBottom: 18,
    textAlign: 'center',
    letterSpacing: 1.2,
    textTransform: 'capitalize',
    fontFamily: 'System',
  },
  slideDesc: {
    color: '#E0E7EF',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 28,
    fontFamily: 'System',
    fontWeight: '400',
    letterSpacing: 0.2,
  },
  slideCtaButton: {
    backgroundColor: '#fff',
    borderRadius: 999,
    paddingHorizontal: 36,
    paddingVertical: 14,
    alignSelf: 'center',
    marginTop: 8,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  slideCtaText: {
    color: '#1D4ED8',
    fontWeight: 'bold',
    fontSize: 17,
    letterSpacing: 1.5,
    textTransform: 'uppercase',
    fontFamily: 'System',
  },
  dotsCol: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 16,
    gap: 10,
  },
  dot: {
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#E5E7EB',
    marginHorizontal: 5,
  },
  dotActive: {
    backgroundColor: '#6366F1',
    borderWidth: 2,
    borderColor: '#fff',
  },
}); 