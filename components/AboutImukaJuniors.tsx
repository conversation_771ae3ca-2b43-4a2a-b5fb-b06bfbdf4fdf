import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { Dimensions, Image, StyleSheet, Text, View } from 'react-native';

const windowWidth = Dimensions.get('window').width;
const isMobile = windowWidth < 800;

const VALUES = [
  { icon: 'rocket-outline', label: 'Innovation' },
  { icon: 'people-outline', label: 'Community' },
  { icon: 'bulb-outline', label: 'Creativity' },
  { icon: 'school-outline', label: 'Learning' },
];

const IMPACT = [
  { label: 'Juniors Empowered', value: '100+' },
  { label: 'Bootcamps Held', value: '12' },
  { label: 'Mentors', value: '25+' },
];

export default function AboutImukaJuniors() {
  return (
    <LinearGradient
      colors={["#f8fafc", "#eef2ff"]}
      style={styles.gradientBg}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <View style={[styles.container, isMobile && { flexDirection: 'column-reverse', gap: 32 }]}> 
        {/* Left: Text & Values */}
        <View style={styles.textCol}>
          <Text style={styles.subtitle}>About Us</Text>
          <Text style={styles.title}>Empowering the Next Generation of Entrepreneurs</Text>
          <Text style={styles.mission}>
            Our mission is to nurture young talent, foster innovation, and build a vibrant entrepreneurial community. We believe in the power of creativity, collaboration, and lifelong learning.
          </Text>
          <View style={styles.valuesRow}>
            {VALUES.map((val, idx) => (
              <View key={idx} style={styles.valueItem}>
                <Ionicons name={val.icon as React.ComponentProps<typeof Ionicons>["name"]} size={28} color="#6366F1" style={styles.valueIcon} />
                <Text style={styles.valueLabel}>{val.label}</Text>
              </View>
            ))}
          </View>
          <View style={styles.impactRow}>
            {IMPACT.map((item, idx) => (
              <View key={idx} style={styles.impactItem}>
                <Text style={styles.impactValue}>{item.value}</Text>
                <Text style={styles.impactLabel}>{item.label}</Text>
              </View>
            ))}
          </View>
          <View style={styles.quoteBox}>
            <Text style={styles.quote}>
              “Imuka Juniors gave me the confidence and skills to launch my first business. The community is truly inspiring!”
            </Text>
            <Text style={styles.quoteAuthor}>— Aisha, Alumni</Text>
          </View>
        </View>
        {/* Right: Visual/Illustration */}
        <View style={styles.visualCol}>
          <View style={styles.visualWrapper}>
            <Image
              source={{ uri: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=600&q=80' }}
              style={styles.heroImage}
              resizeMode="cover"
            />
            {/* SVG/Shape overlay for extra flair */}
            <View style={styles.svgShape}>
              <svg width="180" height="80" viewBox="0 0 180 80" style={{ position: 'absolute', left: 0, top: 0 }} xmlns="http://www.w3.org/2000/svg">
                <path d="M0,40 Q45,0 90,40 T180,40 V80 H0 Z" fill="#6366F1" fillOpacity="0.08" />
              </svg>
            </View>
          </View>
        </View>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  gradientBg: {
    flex: 1,
    borderRadius: 36,
    paddingVertical: 32,
    paddingHorizontal: 0,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
    paddingVertical: 48,
    maxWidth: 1200,
    alignSelf: 'center',
    gap: 32,
    backgroundColor: 'rgba(255,255,255,0.92)',
    borderRadius: 32,
    boxShadow: '0 6px 32px rgba(0,0,0,0.06)',
  },
  textCol: {
    flex: 2,
    minWidth: 280,
    maxWidth: 540,
    justifyContent: 'center',
    gap: 18,
    paddingRight: 8,
  },
  subtitle: {
    fontSize: 15,
    color: '#6366F1',
    fontWeight: '600',
    marginBottom: 2,
    letterSpacing: 2,
    fontFamily: 'System',
    textTransform: 'uppercase',
    opacity: 0.7,
    textAlign: isMobile ? 'center' : 'left',
  },
  title: {
    fontSize: isMobile ? 28 : 38,
    fontWeight: '900',
    color: '#1D4ED8',
    letterSpacing: 1.1,
    fontFamily: 'System',
    textAlign: isMobile ? 'center' : 'left',
    lineHeight: isMobile ? 36 : 44,
    marginBottom: 0,
  },
  mission: {
    fontSize: isMobile ? 16 : 19,
    color: '#374151',
    lineHeight: isMobile ? 26 : 32,
    fontFamily: 'System',
    fontWeight: '400',
    letterSpacing: 0.1,
    textAlign: isMobile ? 'center' : 'left',
    maxWidth: 520,
    opacity: 0.98,
    marginTop: 8,
    marginBottom: 8,
  },
  valuesRow: {
    flexDirection: 'row',
    gap: 18,
    marginVertical: 8,
    justifyContent: isMobile ? 'center' : 'flex-start',
  },
  valueItem: {
    alignItems: 'center',
    gap: 2,
  },
  valueIcon: {
    marginBottom: 2,
  },
  valueLabel: {
    fontSize: 13,
    color: '#6366F1',
    fontWeight: '600',
    fontFamily: 'System',
    textTransform: 'uppercase',
    letterSpacing: 1.1,
  },
  impactRow: {
    flexDirection: 'row',
    gap: 32,
    marginVertical: 10,
    justifyContent: isMobile ? 'center' : 'flex-start',
  },
  impactItem: {
    alignItems: 'center',
  },
  impactValue: {
    fontSize: 22,
    fontWeight: '900',
    color: '#1D4ED8',
    fontFamily: 'System',
    marginBottom: 2,
  },
  impactLabel: {
    fontSize: 13,
    color: '#374151',
    fontFamily: 'System',
    opacity: 0.8,
    textTransform: 'uppercase',
    letterSpacing: 1.1,
  },
  quoteBox: {
    backgroundColor: '#EEF2FF',
    borderRadius: 18,
    padding: 18,
    marginTop: 18,
    marginBottom: 0,
    maxWidth: 420,
    alignSelf: isMobile ? 'center' : 'flex-start',
    boxShadow: '0 2px 12px rgba(99,102,241,0.08)',
  },
  quote: {
    fontSize: 16,
    color: '#6366F1',
    fontStyle: 'italic',
    marginBottom: 6,
    fontFamily: 'System',
    textAlign: isMobile ? 'center' : 'left',
  },
  quoteAuthor: {
    fontSize: 14,
    color: '#1D4ED8',
    fontWeight: 'bold',
    fontFamily: 'System',
    textAlign: isMobile ? 'center' : 'left',
  },
  visualCol: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 220,
    maxWidth: 340,
    padding: 0,
    position: 'relative',
  },
  visualWrapper: {
    width: 260,
    height: 320,
    borderRadius: 32,
    backgroundColor: '#fff',
    overflow: 'hidden',
    boxShadow: '0 4px 24px rgba(99,102,241,0.10)',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroImage: {
    width: 260,
    height: 320,
    borderRadius: 32,
    objectFit: 'cover',
  },
  svgShape: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: 180,
    height: 80,
    zIndex: 2,
    pointerEvents: 'none',
  },
}); 