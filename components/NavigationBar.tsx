import React, { useState } from 'react';
import { Modal, Platform, Pressable, ScrollView, StyleSheet, Text, useWindowDimensions, View } from 'react-native';
import { isBreakpoint } from '../constants/DesignSystem';

export default function NavigationBar() {
  const { width } = useWindowDimensions();
  const isMobile = !isBreakpoint('lg', width);
  const [menuOpen, setMenuOpen] = useState(false);

  return (
    <View style={styles.navbar}>
      <View style={styles.navbarContent}>
        {/* Logo */}
        <Pressable style={styles.logoContainer} accessibilityLabel="Home">
          <View style={styles.logoWrapper}>
            <View style={styles.logoIcon}>
              <Text style={styles.logoIconText}>I</Text>
            </View>
            <Text style={styles.logoText}>Imuka Juniors</Text>
          </View>
        </Pressable>

        {/* Desktop Navigation Links (center) */}
        {!isMobile && (
          <View style={styles.navLinksContainer}>
            <Pressable style={styles.navLink} accessibilityLabel="Courses">
              {({ hovered }) => (
                <Text style={[styles.navLinkText, hovered && styles.navLinkHover]}>Courses</Text>
              )}
            </Pressable>
            <Pressable style={styles.navLink} accessibilityLabel="Bootcamps">
              {({ hovered }) => (
                <Text style={[styles.navLinkText, hovered && styles.navLinkHover]}>Bootcamps</Text>
              )}
            </Pressable>
            <Pressable style={styles.navLink} accessibilityLabel="About">
              {({ hovered }) => (
                <Text style={[styles.navLinkText, hovered && styles.navLinkHover]}>About</Text>
              )}
            </Pressable>
            <Pressable style={styles.navLink} accessibilityLabel="Contact">
              {({ hovered }) => (
                <Text style={[styles.navLinkText, hovered && styles.navLinkHover]}>Contact</Text>
              )}
            </Pressable>
          </View>
        )}

        {/* Desktop Auth Buttons (right) */}
        {!isMobile && (
          <View style={styles.authContainer}>
            <Pressable style={styles.loginButton} accessibilityLabel="Login">
              {({ hovered }) => (
                <Text style={[styles.loginButtonText, hovered && styles.loginButtonHover]}>Sign In</Text>
              )}
            </Pressable>
            <Pressable style={styles.registerButton} accessibilityLabel="Register">
              {({ hovered }) => (
                <Text style={[styles.registerButtonText, hovered && styles.registerButtonHover]}>Get Started</Text>
              )}
            </Pressable>
          </View>
        )}

        {/* Mobile Menu Button */}
        {isMobile && (
          <Pressable style={styles.mobileMenuButton} onPress={() => setMenuOpen(true)} accessibilityLabel="Open menu">
            <View style={styles.hamburgerLine} />
            <View style={styles.hamburgerLine} />
            <View style={styles.hamburgerLine} />
          </Pressable>
        )}
      </View>

      {/* Mobile Menu Modal */}
      {isMobile && (
        <Modal
          visible={menuOpen}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setMenuOpen(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.mobileMenu}>
              <View style={styles.mobileMenuHeader}>
                <View style={styles.logoWrapper}>
                  <View style={styles.logoIcon}>
                    <Text style={styles.logoIconText}>I</Text>
                  </View>
                  <Text style={styles.logoText}>Imuka Juniors</Text>
                </View>
                <Pressable style={styles.closeButton} onPress={() => setMenuOpen(false)} accessibilityLabel="Close menu">
                  <View style={styles.closeIcon}>
                    <View style={[styles.closeLine, { transform: [{ rotate: '45deg' }] }]} />
                    <View style={[styles.closeLine, { transform: [{ rotate: '-45deg' }] }]} />
                  </View>
                </Pressable>
              </View>

              <ScrollView contentContainerStyle={styles.mobileMenuContent}>
                <View style={styles.mobileNavLinks}>
                  <Pressable style={styles.mobileNavLink} onPress={() => setMenuOpen(false)}>
                    <Text style={styles.mobileNavLinkText}>Courses</Text>
                  </Pressable>
                  <Pressable style={styles.mobileNavLink} onPress={() => setMenuOpen(false)}>
                    <Text style={styles.mobileNavLinkText}>Bootcamps</Text>
                  </Pressable>
                  <Pressable style={styles.mobileNavLink} onPress={() => setMenuOpen(false)}>
                    <Text style={styles.mobileNavLinkText}>About</Text>
                  </Pressable>
                  <Pressable style={styles.mobileNavLink} onPress={() => setMenuOpen(false)}>
                    <Text style={styles.mobileNavLinkText}>Contact</Text>
                  </Pressable>
                </View>

                <View style={styles.mobileAuthButtons}>
                  <Pressable style={styles.mobileLoginButton} onPress={() => setMenuOpen(false)}>
                    <Text style={styles.mobileLoginButtonText}>Sign In</Text>
                  </Pressable>
                  <Pressable style={styles.mobileRegisterButton} onPress={() => setMenuOpen(false)}>
                    <Text style={styles.mobileRegisterButtonText}>Get Started</Text>
                  </Pressable>
                </View>
              </ScrollView>
            </View>
          </View>
        </Modal>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  navbar: {
    backgroundColor: DesignSystem.colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: DesignSystem.colors.neutral[200],
    shadowColor: DesignSystem.colors.neutral[900],
    shadowOpacity: Platform.OS === 'web' ? 0.03 : 0.05,
    shadowRadius: 12,
    shadowOffset: { width: 0, height: 1 },
    elevation: 3,
    position: 'sticky',
    top: 0,
    zIndex: DesignSystem.zIndex.sticky,
  },
  navbarContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: DesignSystem.spacing[4],
    paddingHorizontal: DesignSystem.spacing[6],
    maxWidth: 1280,
    width: '100%',
    alignSelf: 'center',
  },
  logoContainer: {
    flex: 0,
  },
  logoWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: DesignSystem.spacing[3],
  },
  logoIcon: {
    width: 40,
    height: 40,
    borderRadius: DesignSystem.borderRadius.lg,
    backgroundColor: DesignSystem.colors.primary[600],
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: DesignSystem.colors.primary[600],
    shadowOpacity: 0.3,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 4,
  },
  logoIconText: {
    fontSize: DesignSystem.typography.fontSize.xl,
    fontWeight: DesignSystem.typography.fontWeight.bold,
    color: DesignSystem.colors.text.inverse,
    fontFamily: DesignSystem.typography.fonts.heading,
  },
  logoText: {
    fontSize: DesignSystem.typography.fontSize['2xl'],
    fontWeight: DesignSystem.typography.fontWeight.bold,
    color: DesignSystem.colors.text.brand,
    fontFamily: DesignSystem.typography.fonts.heading,
    letterSpacing: DesignSystem.typography.letterSpacing.tight,
  },
  navLinksContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: DesignSystem.spacing[8],
    flex: 1,
    justifyContent: 'center',
  },
  navLink: {
    paddingVertical: DesignSystem.spacing[2],
    paddingHorizontal: DesignSystem.spacing[3],
    borderRadius: DesignSystem.borderRadius.md,
  },
  navLinkText: {
    fontSize: DesignSystem.typography.fontSize.base,
    fontWeight: DesignSystem.typography.fontWeight.medium,
    color: DesignSystem.colors.text.secondary,
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  navLinkHover: {
    color: DesignSystem.colors.primary[600],
  },
  authContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: DesignSystem.spacing[3],
    flex: 0,
  },
  loginButton: {
    paddingHorizontal: DesignSystem.spacing[4],
    paddingVertical: DesignSystem.spacing[2],
    borderRadius: DesignSystem.borderRadius.lg,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  loginButtonText: {
    fontSize: DesignSystem.typography.fontSize.base,
    fontWeight: DesignSystem.typography.fontWeight.semibold,
    color: DesignSystem.colors.text.secondary,
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  loginButtonHover: {
    color: DesignSystem.colors.primary[600],
  },
  registerButton: {
    paddingHorizontal: DesignSystem.spacing[6],
    paddingVertical: DesignSystem.spacing[3],
    borderRadius: DesignSystem.borderRadius.full,
    backgroundColor: DesignSystem.colors.primary[600],
    shadowColor: DesignSystem.colors.primary[600],
    shadowOpacity: 0.25,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 4,
  },
  registerButtonText: {
    fontSize: DesignSystem.typography.fontSize.base,
    fontWeight: DesignSystem.typography.fontWeight.semibold,
    color: DesignSystem.colors.text.inverse,
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  registerButtonHover: {
    color: DesignSystem.colors.text.inverse,
  },
  mobileMenuButton: {
    padding: DesignSystem.spacing[2],
    borderRadius: DesignSystem.borderRadius.md,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
    gap: DesignSystem.spacing[1],
  },
  hamburgerLine: {
    width: 24,
    height: 2,
    backgroundColor: DesignSystem.colors.text.secondary,
    borderRadius: DesignSystem.borderRadius.full,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'flex-end',
  },
  mobileMenu: {
    backgroundColor: DesignSystem.colors.background.primary,
    borderTopLeftRadius: DesignSystem.borderRadius['3xl'],
    borderTopRightRadius: DesignSystem.borderRadius['3xl'],
    paddingTop: DesignSystem.spacing[6],
    paddingBottom: DesignSystem.spacing[8],
    paddingHorizontal: DesignSystem.spacing[6],
    minHeight: '50%',
    shadowColor: DesignSystem.colors.neutral[900],
    shadowOpacity: 0.1,
    shadowRadius: 20,
    shadowOffset: { width: 0, height: -4 },
    elevation: 10,
  },
  mobileMenuHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: DesignSystem.spacing[8],
    paddingBottom: DesignSystem.spacing[4],
    borderBottomWidth: 1,
    borderBottomColor: DesignSystem.colors.neutral[200],
  },
  closeButton: {
    padding: DesignSystem.spacing[2],
    borderRadius: DesignSystem.borderRadius.md,
    backgroundColor: DesignSystem.colors.neutral[100],
  },
  closeIcon: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  closeLine: {
    position: 'absolute',
    width: 16,
    height: 2,
    backgroundColor: DesignSystem.colors.text.secondary,
    borderRadius: DesignSystem.borderRadius.full,
  },
  mobileMenuContent: {
    flex: 1,
    paddingTop: DesignSystem.spacing[4],
  },
  mobileNavLinks: {
    gap: DesignSystem.spacing[2],
    marginBottom: DesignSystem.spacing[8],
  },
  mobileNavLink: {
    paddingVertical: DesignSystem.spacing[4],
    paddingHorizontal: DesignSystem.spacing[4],
    borderRadius: DesignSystem.borderRadius.lg,
    backgroundColor: DesignSystem.colors.background.secondary,
    marginBottom: DesignSystem.spacing[2],
  },
  mobileNavLinkText: {
    fontSize: DesignSystem.typography.fontSize.lg,
    fontWeight: DesignSystem.typography.fontWeight.semibold,
    color: DesignSystem.colors.text.primary,
    fontFamily: DesignSystem.typography.fonts.primary,
    textAlign: 'center',
  },
  mobileAuthButtons: {
    gap: DesignSystem.spacing[4],
    marginTop: 'auto',
  },
  mobileLoginButton: {
    paddingVertical: DesignSystem.spacing[4],
    paddingHorizontal: DesignSystem.spacing[6],
    borderRadius: DesignSystem.borderRadius.full,
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: DesignSystem.colors.primary[600],
    alignItems: 'center',
  },
  mobileLoginButtonText: {
    fontSize: DesignSystem.typography.fontSize.lg,
    fontWeight: DesignSystem.typography.fontWeight.semibold,
    color: DesignSystem.colors.primary[600],
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  mobileRegisterButton: {
    paddingVertical: DesignSystem.spacing[4],
    paddingHorizontal: DesignSystem.spacing[6],
    borderRadius: DesignSystem.borderRadius.full,
    backgroundColor: DesignSystem.colors.primary[600],
    alignItems: 'center',
    shadowColor: DesignSystem.colors.primary[600],
    shadowOpacity: 0.25,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 4,
  },
  mobileRegisterButtonText: {
    fontSize: DesignSystem.typography.fontSize.lg,
    fontWeight: DesignSystem.typography.fontWeight.semibold,
    color: DesignSystem.colors.text.inverse,
    fontFamily: DesignSystem.typography.fonts.primary,
  },
});