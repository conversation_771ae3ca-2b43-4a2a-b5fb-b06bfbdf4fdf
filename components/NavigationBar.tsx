import React, { useState } from 'react';
import { Modal, Platform, Pressable, ScrollView, StyleSheet, Text, TextInput, useWindowDimensions, View } from 'react-native';

export default function NavigationBar() {
  const { width } = useWindowDimensions();
  const isMobile = width < 800;
  const [menuOpen, setMenuOpen] = useState(false);

  return (
    <View style={styles.navbar}>
      {/* Logo */}
      <Pressable style={styles.logoContainer} accessibilityLabel="Home">
        <Text style={styles.logoText}>Imuka Juniors</Text>
      </Pressable>

      {/* Search Bar (center, wide) - hide on mobile */}
      {!isMobile && (
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="Search..."
            placeholderTextColor="#6B7280"
            accessibilityLabel="Search"
          />
        </View>
      )}

      {/* Desktop Navigation Links (right) */}
      {!isMobile && (
        <View style={styles.rightContainer}>
          <Pressable style={styles.navLink} accessibilityLabel="Bootcamps">
            {({ hovered }) => (
              <Text style={[styles.navLinkText, hovered && styles.navLinkHover]}>Bootcamps</Text>
            )}
          </Pressable>
          <Pressable style={styles.navLink} accessibilityLabel="Courses">
            {({ hovered }) => (
              <Text style={[styles.navLinkText, hovered && styles.navLinkHover]}>Courses</Text>
            )}
          </Pressable>
          <Pressable style={styles.navLink} accessibilityLabel="Business Manager">
            {({ hovered }) => (
              <Text style={[styles.navLinkText, hovered && styles.navLinkHover]}>Business Manager</Text>
            )}
          </Pressable>
          {/* Login/Register Button Group */}
          <View style={styles.authButtonGroup}>
            <Pressable style={styles.loginButton} accessibilityLabel="Login">
              <Text style={styles.loginButtonText}>Login</Text>
            </Pressable>
            <Pressable style={styles.registerButton} accessibilityLabel="Register">
              <Text style={styles.registerButtonText}>Register</Text>
            </Pressable>
          </View>
        </View>
      )}

      {/* Hamburger menu for mobile */}
      {isMobile && (
        <>
          <Pressable style={styles.hamburger} onPress={() => setMenuOpen(true)} accessibilityLabel="Open menu">
            <Text style={{ fontSize: 28, color: '#1D4ED8' }}>☰</Text>
          </Pressable>
          <Modal
            visible={menuOpen}
            animationType="slide"
            transparent={true}
            onRequestClose={() => setMenuOpen(false)}
          >
            <View style={styles.modalOverlay}>
              <View style={styles.menuModal}>
                <Pressable style={styles.closeButton} onPress={() => setMenuOpen(false)} accessibilityLabel="Close menu">
                  <Text style={{ fontSize: 28, color: '#1D4ED8' }}>×</Text>
                </Pressable>
                <ScrollView contentContainerStyle={styles.menuContent}>
                  <Pressable style={styles.menuLink} onPress={() => setMenuOpen(false)} accessibilityLabel="Bootcamps"><Text style={styles.menuLinkText}>Bootcamps</Text></Pressable>
                  <View style={styles.menuDivider} />
                  <Pressable style={styles.menuLink} onPress={() => setMenuOpen(false)} accessibilityLabel="Courses"><Text style={styles.menuLinkText}>Courses</Text></Pressable>
                  <View style={styles.menuDivider} />
                  <Pressable style={styles.menuLink} onPress={() => setMenuOpen(false)} accessibilityLabel="Business Manager"><Text style={styles.menuLinkText}>Business Manager</Text></Pressable>
                  <View style={styles.menuDivider} />
                  <View style={styles.menuAuthGroup}>
                    <Pressable style={styles.menuLoginButton} accessibilityLabel="Login"><Text style={styles.menuLoginButtonText}>Login</Text></Pressable>
                    <Pressable style={styles.menuRegisterButton} accessibilityLabel="Register"><Text style={styles.menuRegisterButtonText}>Register</Text></Pressable>
                  </View>
                </ScrollView>
              </View>
            </View>
          </Modal>
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  navbar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 32,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOpacity: Platform.OS === 'web' ? 0.04 : 0.08,
    shadowRadius: 8,
    elevation: 2,
    position: 'sticky',
    top: 0,
    zIndex: 100,
  },
  logoContainer: { marginRight: 16 },
  logoText: { fontSize: 24, fontWeight: 'bold', color: '#1D4ED8', letterSpacing: 1 },
  searchContainer: {
    flex: 1,
    marginHorizontal: 32,
    minWidth: 400,
    maxWidth: 600,
  },
  searchInput: {
    backgroundColor: '#F3F4F6',
    borderRadius: 24,
    paddingHorizontal: 24,
    paddingVertical: 12,
    fontSize: 18,
    color: '#111827',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginLeft: 16,
  },
  navLink: { marginHorizontal: 8 },
  navLinkText: { fontSize: 16, color: '#374151', fontWeight: '500' },
  navLinkHover: {
    color: '#1D4ED8',
    textDecorationLine: 'underline',
  },
  authButtonGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 16,
    backgroundColor: '#E0E7FF',
    borderRadius: 999,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: '#1D4ED8',
  },
  loginButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderRightWidth: 2,
    borderColor: '#1D4ED8',
  },
  loginButtonText: {
    color: '#1D4ED8',
    fontWeight: 'bold',
    fontSize: 16,
  },
  registerButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#1D4ED8',
    borderLeftWidth: 2,
    borderColor: '#1D4ED8',
  },
  registerButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  hamburger: {
    marginLeft: 16,
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#E0E7FF',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'flex-end',
  },
  menuModal: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 24,
    minHeight: 320,
  },
  closeButton: {
    alignSelf: 'flex-end',
    marginBottom: 16,
  },
  menuContent: {
    alignItems: 'center',
    gap: 0,
    width: '100%',
  },
  menuLink: {
    width: '100%',
    alignItems: 'center',
    paddingVertical: 18,
    paddingHorizontal: 8,
  },
  menuLinkText: {
    fontSize: 22,
    color: '#1D4ED8',
    fontWeight: 'bold',
    letterSpacing: 1,
  },
  menuDivider: {
    width: '80%',
    alignSelf: 'center',
    height: 1,
    backgroundColor: '#E5E7EB',
    marginVertical: 2,
  },
  menuAuthGroup: {
    flexDirection: 'row',
    marginTop: 32,
    alignSelf: 'stretch',
    justifyContent: 'space-between',
    gap: 12,
  },
  menuLoginButton: {
    flex: 1,
    paddingVertical: 14,
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#1D4ED8',
    borderRadius: 999,
    alignItems: 'center',
    marginRight: 4,
  },
  menuLoginButtonText: {
    color: '#1D4ED8',
    fontWeight: 'bold',
    fontSize: 20,
  },
  menuRegisterButton: {
    flex: 1,
    paddingVertical: 14,
    backgroundColor: '#1D4ED8',
    borderWidth: 2,
    borderColor: '#1D4ED8',
    borderRadius: 999,
    alignItems: 'center',
    marginLeft: 4,
  },
  menuRegisterButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 20,
  },
}); 