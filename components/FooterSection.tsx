import React from 'react';
import { Linking, Pressable, StyleSheet, Text, View } from 'react-native';

export default function FooterSection() {
  return (
    <View style={styles.footer}>
      <View style={styles.linksRow}>
        <Pressable onPress={() => Linking.openURL('#')}><Text style={styles.link}>Home</Text></Pressable>
        <Pressable onPress={() => Linking.openURL('#')}><Text style={styles.link}>Bootcamps</Text></Pressable>
        <Pressable onPress={() => Linking.openURL('#')}><Text style={styles.link}>Courses</Text></Pressable>
        <Pressable onPress={() => Linking.openURL('#')}><Text style={styles.link}>About</Text></Pressable>
      </View>
      <View style={styles.socialRow}>
        <Pressable onPress={() => Linking.openURL('https://twitter.com/')}><Text style={styles.socialIcon}>🐦</Text></Pressable>
        <Pressable onPress={() => Linking.openURL('https://facebook.com/')}><Text style={styles.socialIcon}>📘</Text></Pressable>
        <Pressable onPress={() => Linking.openURL('https://linkedin.com/')}><Text style={styles.socialIcon}>💼</Text></Pressable>
      </View>
      <Text style={styles.copyright}>© {new Date().getFullYear()} Imuka Juniors. All rights reserved.</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  footer: {
    backgroundColor: '#F3F4F6',
    paddingVertical: 40,
    paddingHorizontal: 24,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    marginTop: 0,
  },
  linksRow: {
    flexDirection: 'row',
    gap: 32,
    marginBottom: 16,
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  link: {
    color: '#1D4ED8',
    fontWeight: 'bold',
    fontSize: 16,
    letterSpacing: 0.5,
    fontFamily: 'System',
    marginHorizontal: 8,
  },
  socialRow: {
    flexDirection: 'row',
    gap: 20,
    marginBottom: 16,
    justifyContent: 'center',
  },
  socialIcon: {
    fontSize: 24,
    marginHorizontal: 8,
  },
  copyright: {
    color: '#6B7280',
    fontSize: 15,
    fontFamily: 'System',
    marginTop: 8,
    textAlign: 'center',
  },
}); 