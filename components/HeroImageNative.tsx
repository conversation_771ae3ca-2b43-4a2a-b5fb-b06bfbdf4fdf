import React from 'react';
import Svg, { ClipP<PERSON>, Defs, Path, Image as SvgImage } from 'react-native-svg';

export default function HeroImageNative({ isMobile }: { isMobile: boolean }) {
  return (
    <Svg
      width={isMobile ? 180 : 400}
      height={isMobile ? 180 : 400}
      viewBox="0 0 400 400"
      style={isMobile ? { width: 180, height: 180 } : { width: 400, height: 400 }}
    >
      <Defs>
        <ClipPath id="clip">
          {/* Wavy bottom edge for a modern look */}
          <Path d="M0,0 H400 V340 C320,380 80,380 0,340 Z" />
        </ClipPath>
      </Defs>
      <SvgImage
        width={isMobile ? 180 : 400}
        height={isMobile ? 180 : 400}
        href={{ uri: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=600&q=80' }}
        preserveAspectRatio="xMidYMid slice"
        clipPath="url(#clip)"
      />
    </Svg>
  );
} 