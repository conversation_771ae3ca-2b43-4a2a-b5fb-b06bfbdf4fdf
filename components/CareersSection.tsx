import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { Image, Platform, Pressable, StyleSheet, Text, View } from 'react-native';

const CAREERS = [
  {
    title: 'Discover',
    description: 'Start your career journey and explore foundational skills. Perfect for beginners and those curious about new opportunities.',
    image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=400&q=80' },
  },
  {
    title: 'Advance',
    description: 'Grow your skills and take your career to the next level. Ideal for those ready to expand and optimize.',
    image: { uri: 'https://images.unsplash.com/photo-1503676382389-4809596d5290?auto=format&fit=crop&w=400&q=80' },
  },
  {
    title: 'Thrive',
    description: 'Master advanced concepts and expand your impact. For professionals aiming for sustainable growth.',
    image: { uri: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?auto=format&fit=crop&w=400&q=80' },
  },
];

export default function CareersSection() {
  return (
    <LinearGradient
      colors={["#f8fafc", "#e0e7ff"]}
      style={styles.gradientBg}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <View style={styles.section}>
        <View style={styles.titleRow}>
          <View style={styles.titleAccent} />
          <Text style={styles.sectionTitle}>Careers</Text>
        </View>
        <Text style={styles.sectionDesc}>
          Our careers program is structured in three stages to guide you from the basics to advanced mastery. Choose your path and grow at your own pace.
        </Text>
        <View style={styles.levelsRow}>
          {CAREERS.map((career, idx) => (
            <Pressable
              key={idx}
              style={({ hovered }) => [
                styles.card,
                hovered && Platform.OS === 'web' && styles.cardHover,
                { borderTopColor: ['#6366F1', '#F59E42', '#10B981'][idx], borderTopWidth: 6 },
              ]}
            >
              <View style={styles.imageWrapper}>
                <Image source={career.image} style={styles.image} resizeMode="cover" />
              </View>
              <Text style={styles.levelTitle}>{career.title}</Text>
              <Text style={styles.levelDesc}>{career.description}</Text>
            </Pressable>
          ))}
        </View>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  gradientBg: {
    width: '100%',
    paddingVertical: 48,
    paddingHorizontal: 0,
  },
  section: {
    maxWidth: 1100,
    alignSelf: 'center',
    width: '100%',
    paddingHorizontal: 24,
    paddingVertical: 0,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    marginBottom: 8,
  },
  titleAccent: {
    width: 6,
    height: 36,
    borderRadius: 3,
    backgroundColor: '#6366F1',
  },
  sectionTitle: {
    fontSize: 32,
    fontWeight: '900',
    color: '#1D4ED8',
    letterSpacing: 1.2,
    fontFamily: 'System',
    textAlign: 'center',
  },
  sectionDesc: {
    fontSize: 17,
    color: '#374151',
    marginBottom: 36,
    fontFamily: 'System',
    textAlign: 'center',
    lineHeight: 28,
    maxWidth: 600,
    alignSelf: 'center',
    opacity: 0.92,
    fontWeight: '400',
  },
  levelsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 32,
    flexWrap: 'wrap',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 24,
    alignItems: 'center',
    padding: 36,
    width: 320,
    height: 400,
    marginBottom: 24,
    shadowColor: '#6366F1',
    shadowOpacity: 0.10,
    shadowRadius: 16,
    elevation: 4,
    borderTopWidth: 6,
    borderTopColor: '#6366F1',
    transitionProperty: Platform.OS === 'web' ? 'box-shadow, transform' : undefined,
    transitionDuration: Platform.OS === 'web' ? '0.2s' : undefined,
  },
  cardHover: {
    shadowOpacity: 0.18,
    shadowRadius: 24,
    elevation: 8,
    transform: [{ scale: 1.04 }],
  },
  imageWrapper: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#EEF2FF',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 22,
    shadowColor: '#6366F1',
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
  image: {
    width: 88,
    height: 88,
    borderRadius: 44,
    backgroundColor: '#fff',
    objectFit: 'cover',
  },
  levelTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1D4ED8',
    marginBottom: 12,
    letterSpacing: 0.8,
    fontFamily: 'System',
    textAlign: 'center',
  },
  levelDesc: {
    fontSize: 15,
    color: '#374151',
    textAlign: 'center',
    lineHeight: 22,
    fontFamily: 'System',
    fontWeight: '400',
    opacity: 0.96,
  },
}); 