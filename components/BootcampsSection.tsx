import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { Image, Platform, Pressable, StyleSheet, Text, View } from 'react-native';

type BootcampStatus = 'upcoming' | 'ongoing' | 'past';

const BOOTCAMPS: {
  title: string;
  date: string;
  description: string;
  status: BootcampStatus;
  image: { uri: string };
}[] = [
  {
    title: 'Entrepreneurship 101',
    date: 'July 15, 2024',
    description: 'Learn the basics of starting and running a business.',
    status: 'upcoming',
    image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=400&q=80' },
  },
  {
    title: 'Digital Marketing Bootcamp',
    date: 'August 5, 2024',
    description: 'Master online marketing strategies for your business.',
    status: 'upcoming',
    image: { uri: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80' },
  },
  {
    title: 'Startup Growth Camp',
    date: 'June 1, 2024',
    description: 'Take your startup to the next level with expert guidance.',
    status: 'ongoing',
    image: { uri: 'https://images.unsplash.com/photo-1504384308090-c894fdcc538d?auto=format&fit=crop&w=400&q=80' },
  },
  {
    title: 'Leadership Bootcamp',
    date: 'May 2024',
    description: 'Develop leadership skills for business success.',
    status: 'past',
    image: { uri: 'https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=400&q=80' },
  },
];

const statusColors: Record<BootcampStatus, string> = {
  upcoming: '#1D4ED8',
  ongoing: '#F59E42',
  past: '#6B7280',
};

export default function BootcampsSection() {
  return (
    <LinearGradient
      colors={["#f8fafc", "#e0e7ff"]}
      style={styles.gradientBg}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <View style={styles.section}>
        <View style={styles.titleRow}>
          <View style={styles.titleAccent} />
          <Text style={styles.sectionTitle}>Bootcamps</Text>
        </View>
        <Text style={styles.sectionDesc}>
          Join our hands-on bootcamps to gain practical skills, connect with mentors, and accelerate your entrepreneurial journey. Upcoming, ongoing, and past bootcamps are listed below.
        </Text>
        <View style={styles.bootcampsList}>
          {BOOTCAMPS.slice(0, 3).map((bootcamp, idx) => (
            <Pressable
              key={idx}
              style={({ hovered }) => [
                styles.card,
                hovered && Platform.OS === 'web' && styles.cardHover,
                { borderTopColor: statusColors[bootcamp.status], borderTopWidth: 6 },
              ]}
            >
              <View style={styles.imageWrapper}>
                <Image source={bootcamp.image} style={styles.image} resizeMode="cover" />
              </View>
              <View style={styles.cardContent}>
                <Text style={styles.cardTitle}>{bootcamp.title}</Text>
                <View style={styles.cardMetaRow}>
                  <Text style={styles.cardDate}>{bootcamp.date}</Text>
                  <Text style={[styles.status, { backgroundColor: statusColors[bootcamp.status] }]}>{bootcamp.status.toUpperCase()}</Text>
                </View>
                <Text style={styles.cardDesc}>{bootcamp.description}</Text>
              </View>
            </Pressable>
          ))}
        </View>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  gradientBg: {
    width: '100%',
    paddingVertical: 48,
    paddingHorizontal: 0,
  },
  section: {
    maxWidth: 1100,
    alignSelf: 'center',
    width: '100%',
    paddingHorizontal: 24,
    paddingVertical: 0,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    marginBottom: 8,
  },
  titleAccent: {
    width: 6,
    height: 36,
    borderRadius: 3,
    backgroundColor: '#1D4ED8',
  },
  sectionTitle: {
    fontSize: 32,
    fontWeight: '900',
    color: '#1D4ED8',
    letterSpacing: 1.2,
    fontFamily: 'System',
    textAlign: 'center',
  },
  sectionDesc: {
    fontSize: 17,
    color: '#374151',
    marginBottom: 36,
    fontFamily: 'System',
    textAlign: 'center',
    lineHeight: 28,
    maxWidth: 600,
    alignSelf: 'center',
    opacity: 0.92,
    fontWeight: '400',
  },
  bootcampsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 32,
    justifyContent: 'center',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 24,
    width: 320,
    height: 420,
    marginBottom: 24,
    shadowColor: '#1D4ED8',
    shadowOpacity: 0.10,
    shadowRadius: 16,
    elevation: 4,
    borderTopWidth: 6,
    borderTopColor: '#1D4ED8',
    overflow: 'hidden',
    transitionProperty: Platform.OS === 'web' ? 'box-shadow, transform' : undefined,
    transitionDuration: Platform.OS === 'web' ? '0.2s' : undefined,
    alignItems: 'center',
  },
  cardHover: {
    shadowOpacity: 0.18,
    shadowRadius: 24,
    elevation: 8,
    transform: [{ scale: 1.04 }],
  },
  imageWrapper: {
    width: 110,
    height: 110,
    borderRadius: 55,
    backgroundColor: '#EEF2FF',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 18,
    marginBottom: 18,
    shadowColor: '#1D4ED8',
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#fff',
    objectFit: 'cover',
  },
  cardContent: {
    flex: 1,
    padding: 18,
    justifyContent: 'flex-start',
    alignItems: 'center',
    width: '100%',
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1D4ED8',
    fontFamily: 'System',
    marginBottom: 10,
    textAlign: 'center',
  },
  cardMetaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    justifyContent: 'center',
    gap: 12,
  },
  status: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 13,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 4,
    overflow: 'hidden',
    fontFamily: 'System',
    marginLeft: 8,
  },
  cardDate: {
    fontSize: 15,
    color: '#6B7280',
    fontFamily: 'System',
  },
  cardDesc: {
    fontSize: 15,
    color: '#374151',
    fontFamily: 'System',
    lineHeight: 22,
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.96,
  },
}); 