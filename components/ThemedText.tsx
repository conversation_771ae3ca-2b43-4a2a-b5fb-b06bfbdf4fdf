import Typography from '@/constants/Typography';
import { useThemeColor } from '@/hooks/useThemeColor';
import { StyleSheet, Text, type TextProps } from 'react-native';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?:
    | 'default'
    | 'title'
    | 'defaultSemiBold'
    | 'subtitle'
    | 'link'
    | 'hero'
    | 'xxl'
    | 'xl'
    | 'lg'
    | 'md'
    | 'sm'
    | 'xs'
    | 'caption';
};

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = 'default',
  ...rest
}: ThemedTextProps) {
  const color = useThemeColor({ light: lightColor, dark: darkColor }, 'text');

  return (
    <Text
      style={[
        { color },
        type === 'default' ? styles.default : undefined,
        type === 'title' ? styles.title : undefined,
        type === 'defaultSemiBold' ? styles.defaultSemiBold : undefined,
        type === 'subtitle' ? styles.subtitle : undefined,
        type === 'link' ? styles.link : undefined,
        type === 'hero' ? styles.hero : undefined,
        type === 'xxl' ? styles.xxl : undefined,
        type === 'xl' ? styles.xl : undefined,
        type === 'lg' ? styles.lg : undefined,
        type === 'md' ? styles.md : undefined,
        type === 'sm' ? styles.sm : undefined,
        type === 'xs' ? styles.xs : undefined,
        type === 'caption' ? styles.caption : undefined,
        style,
      ]}
      {...rest}
    />
  );
}

const styles = StyleSheet.create({
  default: {
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.lineHeight.base,
    fontFamily: Typography.fontFamily.regular,
    fontWeight: Typography.fontWeight.regular as any,
  },
  defaultSemiBold: {
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.lineHeight.base,
    fontWeight: Typography.fontWeight.semiBold as any,
    fontFamily: Typography.fontFamily.regular,
  },
  title: {
    fontSize: Typography.fontSize.xxl,
    fontWeight: Typography.fontWeight.extraBold as any,
    lineHeight: Typography.lineHeight.loose,
    fontFamily: Typography.fontFamily.regular,
  },
  subtitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold as any,
    fontFamily: Typography.fontFamily.regular,
  },
  link: {
    lineHeight: Typography.lineHeight.relaxed,
    fontSize: Typography.fontSize.base,
    color: '#0a7ea4',
    fontFamily: Typography.fontFamily.regular,
    fontWeight: Typography.fontWeight.medium as any,
  },
  hero: {
    fontSize: Typography.fontSize.hero,
    fontWeight: Typography.fontWeight.extraBold as any,
    lineHeight: Typography.lineHeight.hero,
    fontFamily: Typography.fontFamily.regular,
  },
  xxl: {
    fontSize: Typography.fontSize.xxl,
    fontWeight: Typography.fontWeight.bold as any,
    lineHeight: Typography.lineHeight.loose,
    fontFamily: Typography.fontFamily.regular,
  },
  xl: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold as any,
    lineHeight: Typography.lineHeight.loose,
    fontFamily: Typography.fontFamily.regular,
  },
  lg: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semiBold as any,
    lineHeight: Typography.lineHeight.relaxed,
    fontFamily: Typography.fontFamily.regular,
  },
  md: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.regular as any,
    lineHeight: Typography.lineHeight.relaxed,
    fontFamily: Typography.fontFamily.regular,
  },
  sm: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.regular as any,
    lineHeight: Typography.lineHeight.base,
    fontFamily: Typography.fontFamily.regular,
  },
  xs: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.regular as any,
    lineHeight: Typography.lineHeight.tight,
    fontFamily: Typography.fontFamily.regular,
  },
  caption: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium as any,
    lineHeight: Typography.lineHeight.tight,
    fontFamily: Typography.fontFamily.regular,
    opacity: 0.7,
  },
});
