import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { Platform, Pressable, StyleSheet, Text, View } from 'react-native';

const PILLARS = [
  {
    key: 'discover',
    title: 'Discover',
    color: '#6366F1',
    description: 'Start your journey by exploring the fundamentals of entrepreneurship. Perfect for beginners and those curious about business.',
    courses: [
      { title: 'Entrepreneurship Basics', brief: 'Understand what it means to be an entrepreneur.' },
      { title: 'Idea Validation', brief: 'Learn how to test and validate your business ideas.' },
      { title: 'Market Research', brief: 'Discover your audience and market needs.' },
    ],
  },
  {
    key: 'scale',
    title: 'Scale',
    color: '#F59E42',
    description: 'Take your validated idea and build a scalable business. For those ready to grow and optimize.',
    courses: [
      { title: 'Business Planning', brief: 'Create a roadmap for growth and sustainability.' },
      { title: 'Digital Marketing', brief: 'Reach more customers and grow your brand.' },
      { title: 'Financial Management', brief: 'Master your business finances and funding.' },
    ],
  },
  {
    key: 'grow',
    title: 'Grow',
    color: '#10B981',
    description: 'Expand your impact and master advanced concepts for long-term success.',
    courses: [
      { title: 'Leadership & Team Building', brief: 'Lead teams and build a strong company culture.' },
      { title: 'Scaling Operations', brief: 'Optimize and expand your operations.' },
      { title: 'Sustainable Growth', brief: 'Ensure your business thrives for years to come.' },
    ],
  },
];

export default function EntrepreneurialCoursesSection() {
  return (
    <LinearGradient
      colors={["#f8fafc", "#e0e7ff"]}
      style={styles.gradientBg}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Entrepreneurial Journey</Text>
        <Text style={styles.sectionDesc}>
          Follow our three-stage journey to become a successful entrepreneur. Each stage offers curated courses to help you Discover, Scale, and Grow your business.
        </Text>
        <View style={styles.pillarsRow}>
          {PILLARS.map((pillar) => (
            <View key={pillar.key} style={[styles.pillarCard, { borderTopColor: pillar.color }]}> 
              <View style={[styles.pillarAccent, { backgroundColor: pillar.color }]} />
              <Text style={[styles.pillarTitle, { color: pillar.color }]}>{pillar.title}</Text>
              <Text style={styles.pillarDesc}>{pillar.description}</Text>
              <View style={styles.coursesList}>
                {pillar.courses.map((course, idx) => (
                  <View key={idx} style={styles.courseItem}>
                    <Text style={styles.courseTitle}>{course.title}</Text>
                    <Text style={styles.courseBrief}>{course.brief}</Text>
                  </View>
                ))}
              </View>
              <Pressable style={[styles.ctaButton, { backgroundColor: pillar.color }]}> 
                <Text style={styles.ctaButtonText}>Explore {pillar.title}</Text>
              </Pressable>
            </View>
          ))}
        </View>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  gradientBg: {
    width: '100%',
    paddingVertical: 48,
    paddingHorizontal: 0,
  },
  section: {
    maxWidth: 1200,
    alignSelf: 'center',
    width: '100%',
    paddingHorizontal: 24,
    paddingVertical: 0,
  },
  sectionTitle: {
    fontSize: 32,
    fontWeight: '900',
    color: '#1D4ED8',
    letterSpacing: 1.2,
    fontFamily: 'System',
    textAlign: 'center',
    marginBottom: 8,
  },
  sectionDesc: {
    fontSize: 17,
    color: '#374151',
    marginBottom: 36,
    fontFamily: 'System',
    textAlign: 'center',
    lineHeight: 28,
    maxWidth: 700,
    alignSelf: 'center',
    opacity: 0.92,
    fontWeight: '400',
  },
  pillarsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 32,
    flexWrap: 'wrap',
  },
  pillarCard: {
    backgroundColor: '#fff',
    borderRadius: 24,
    width: 340,
    minHeight: 440,
    marginBottom: 24,
    shadowColor: '#6366F1',
    shadowOpacity: 0.10,
    shadowRadius: 16,
    elevation: 4,
    borderTopWidth: 6,
    borderTopColor: '#6366F1',
    overflow: 'hidden',
    alignItems: 'center',
    padding: 32,
    transitionProperty: Platform.OS === 'web' ? 'box-shadow, transform' : undefined,
    transitionDuration: Platform.OS === 'web' ? '0.2s' : undefined,
  },
  pillarAccent: {
    width: 44,
    height: 6,
    borderRadius: 3,
    marginBottom: 18,
  },
  pillarTitle: {
    fontSize: 24,
    fontWeight: '900',
    letterSpacing: 1.1,
    fontFamily: 'System',
    textAlign: 'center',
    marginBottom: 10,
  },
  pillarDesc: {
    fontSize: 16,
    color: '#374151',
    textAlign: 'center',
    lineHeight: 24,
    fontFamily: 'System',
    fontWeight: '400',
    marginBottom: 18,
    opacity: 0.96,
  },
  coursesList: {
    width: '100%',
    marginBottom: 18,
  },
  courseItem: {
    marginBottom: 12,
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    padding: 12,
  },
  courseTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1D4ED8',
    marginBottom: 2,
    fontFamily: 'System',
  },
  courseBrief: {
    fontSize: 14,
    color: '#374151',
    fontFamily: 'System',
    opacity: 0.92,
  },
  ctaButton: {
    borderRadius: 999,
    paddingHorizontal: 32,
    paddingVertical: 14,
    alignSelf: 'center',
    marginTop: 8,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  ctaButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    letterSpacing: 1.2,
    textTransform: 'uppercase',
    fontFamily: 'System',
  },
}); 