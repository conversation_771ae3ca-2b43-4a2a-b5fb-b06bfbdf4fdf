import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';
import { DesignSystem } from '../constants/DesignSystem';

const LEARNING_PATHS = [
  {
    key: 'discover',
    title: 'Discover',
    level: 'Beginner',
    color: DesignSystem.colors.primary[600],
    gradient: [DesignSystem.colors.primary[500], DesignSystem.colors.primary[700]],
    icon: '🔍',
    description: 'Start your entrepreneurial journey by exploring the fundamentals of business and innovation. Perfect for beginners and curious minds.',
    duration: '4-6 weeks',
    students: '1,200+',
    courses: [
      { title: 'Entrepreneurship Fundamentals', brief: 'Master the core principles of entrepreneurship and business thinking.' },
      { title: 'Idea Generation & Validation', brief: 'Learn systematic approaches to generate and validate business ideas.' },
      { title: 'Market Research Essentials', brief: 'Understand your target audience and market opportunities.' },
      { title: 'Business Model Basics', brief: 'Design sustainable and profitable business models.' },
    ],
    features: ['Interactive workshops', 'Peer networking', 'Mentor guidance', 'Certificate of completion'],
  },
  {
    key: 'scale',
    title: 'Scale',
    level: 'Intermediate',
    color: DesignSystem.colors.accent.orange,
    gradient: ['#f59e0b', '#d97706'],
    icon: '📈',
    description: 'Transform your validated idea into a scalable business. Build systems, processes, and strategies for sustainable growth.',
    duration: '6-8 weeks',
    students: '800+',
    courses: [
      { title: 'Strategic Business Planning', brief: 'Create comprehensive business plans and growth strategies.' },
      { title: 'Digital Marketing Mastery', brief: 'Build powerful online presence and customer acquisition systems.' },
      { title: 'Financial Management & Funding', brief: 'Master business finances, budgeting, and investment strategies.' },
      { title: 'Operations & Systems', brief: 'Design efficient operations and scalable business systems.' },
    ],
    features: ['Real business projects', 'Industry partnerships', 'Funding guidance', 'Advanced certification'],
  },
  {
    key: 'grow',
    title: 'Grow',
    level: 'Advanced',
    color: DesignSystem.colors.accent.green,
    gradient: ['#10b981', '#059669'],
    icon: '🚀',
    description: 'Master advanced entrepreneurship concepts and leadership skills. Build lasting impact and sustainable enterprises.',
    duration: '8-10 weeks',
    students: '500+',
    courses: [
      { title: 'Leadership & Team Excellence', brief: 'Develop exceptional leadership skills and build high-performing teams.' },
      { title: 'Advanced Growth Strategies', brief: 'Master sophisticated growth hacking and expansion techniques.' },
      { title: 'Innovation & Technology', brief: 'Leverage cutting-edge technology for competitive advantage.' },
      { title: 'Social Impact & Sustainability', brief: 'Create businesses that generate positive social and environmental impact.' },
    ],
    features: ['Executive mentorship', 'Investor connections', 'Global network access', 'Master certification'],
  },
];

export default function EntrepreneurialCoursesSection() {
  const { width } = Dimensions.get('window');
  const isMobile = !isBreakpoint('lg', width);

  return (
    <View style={styles.section}>
      <LinearGradient
        colors={[DesignSystem.colors.background.secondary, DesignSystem.colors.background.accent]}
        style={styles.gradientBackground}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.container}>
          {/* Section Header */}
          <View style={styles.sectionHeader}>
            <View style={styles.headerBadge}>
              <Text style={styles.headerBadgeText}>🎓 Learning Paths</Text>
            </View>
            <Text style={styles.sectionTitle}>Your Entrepreneurial Journey</Text>
            <Text style={styles.sectionDescription}>
              Choose your path and master entrepreneurship through our structured learning programs.
              From idea to impact, we'll guide you every step of the way.
            </Text>
          </View>

          {/* Learning Path Cards */}
          <View style={[styles.pathsContainer, isMobile && styles.pathsContainerMobile]}>
            {LEARNING_PATHS.map((path, index) => (
              <View key={path.key} style={[styles.pathCard, isMobile && styles.pathCardMobile]}>
                <LinearGradient
                  colors={path.gradient}
                  style={styles.pathCardGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  {/* Card Header */}
                  <View style={styles.pathCardHeader}>
                    <View style={styles.pathIconContainer}>
                      <Text style={styles.pathIcon}>{path.icon}</Text>
                    </View>
                    <View style={styles.pathHeaderText}>
                      <Text style={styles.pathLevel}>{path.level}</Text>
                      <Text style={styles.pathTitle}>{path.title}</Text>
                    </View>
                    <View style={styles.pathStats}>
                      <Text style={styles.pathDuration}>{path.duration}</Text>
                      <Text style={styles.pathStudents}>{path.students} students</Text>
                    </View>
                  </View>
                </LinearGradient>

                {/* Card Content */}
                <View style={styles.pathCardContent}>
                  <Text style={styles.pathDescription}>{path.description}</Text>

                  {/* Course List */}
                  <View style={styles.coursesList}>
                    <Text style={styles.coursesTitle}>What You'll Learn:</Text>
                    {path.courses.map((course, idx) => (
                      <View key={idx} style={styles.courseItem}>
                        <View style={styles.courseBullet} />
                        <View style={styles.courseContent}>
                          <Text style={styles.courseTitle}>{course.title}</Text>
                          <Text style={styles.courseBrief}>{course.brief}</Text>
                        </View>
                      </View>
                    ))}
                  </View>

                  {/* Features */}
                  <View style={styles.featuresContainer}>
                    <Text style={styles.featuresTitle}>Includes:</Text>
                    <View style={styles.featuresList}>
                      {path.features.map((feature, idx) => (
                        <View key={idx} style={styles.featureItem}>
                          <Text style={styles.featureCheck}>✓</Text>
                          <Text style={styles.featureText}>{feature}</Text>
                        </View>
                      ))}
                    </View>
                  </View>

                  {/* CTA Button */}
                  <Pressable style={[styles.pathCTA, { backgroundColor: path.color }]}>
                    {({ hovered }) => (
                      <Text style={[styles.pathCTAText, hovered && styles.pathCTAHover]}>
                        Start {path.title} Path
                      </Text>
                    )}
                  </Pressable>
                </View>
              </View>
            ))}
          </View>
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  section: {
    width: '100%',
  },
  gradientBackground: {
    paddingVertical: DesignSystem.spacing[20],
    paddingHorizontal: 0,
  },
  container: {
    maxWidth: 1280,
    alignSelf: 'center',
    width: '100%',
    paddingHorizontal: DesignSystem.spacing[6],
  },
  sectionHeader: {
    alignItems: 'center',
    marginBottom: DesignSystem.spacing[16],
  },
  headerBadge: {
    backgroundColor: DesignSystem.colors.background.primary,
    borderRadius: DesignSystem.borderRadius.full,
    paddingHorizontal: DesignSystem.spacing[4],
    paddingVertical: DesignSystem.spacing[2],
    marginBottom: DesignSystem.spacing[4],
    shadowColor: DesignSystem.colors.primary[600],
    shadowOpacity: 0.1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
  },
  headerBadgeText: {
    fontSize: DesignSystem.typography.fontSize.sm,
    fontWeight: DesignSystem.typography.fontWeight.semibold,
    color: DesignSystem.colors.primary[600],
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  sectionTitle: {
    fontSize: DesignSystem.typography.fontSize['5xl'],
    fontWeight: DesignSystem.typography.fontWeight.black,
    color: DesignSystem.colors.text.primary,
    textAlign: 'center',
    marginBottom: DesignSystem.spacing[4],
    fontFamily: DesignSystem.typography.fonts.heading,
    letterSpacing: DesignSystem.typography.letterSpacing.tight,
  },
  sectionDescription: {
    fontSize: DesignSystem.typography.fontSize.xl,
    color: DesignSystem.colors.text.secondary,
    textAlign: 'center',
    lineHeight: DesignSystem.typography.lineHeight.relaxed,
    maxWidth: 700,
    fontFamily: DesignSystem.typography.fonts.primary,
    fontWeight: DesignSystem.typography.fontWeight.normal,
  },
  pathsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: DesignSystem.spacing[8],
    flexWrap: 'wrap',
  },
  pathsContainerMobile: {
    flexDirection: 'column',
    alignItems: 'center',
    gap: DesignSystem.spacing[6],
  },
  pathCard: {
    backgroundColor: DesignSystem.colors.background.primary,
    borderRadius: DesignSystem.borderRadius['3xl'],
    width: 380,
    overflow: 'hidden',
    shadowColor: DesignSystem.colors.neutral[900],
    shadowOpacity: 0.08,
    shadowRadius: 20,
    shadowOffset: { width: 0, height: 4 },
    elevation: 8,
    marginBottom: DesignSystem.spacing[6],
  },
  pathCardMobile: {
    width: '100%',
    maxWidth: 400,
  },
  pathCardGradient: {
    padding: DesignSystem.spacing[6],
    paddingBottom: DesignSystem.spacing[4],
  },
  pathCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  pathIconContainer: {
    width: 56,
    height: 56,
    borderRadius: DesignSystem.borderRadius.xl,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  pathIcon: {
    fontSize: DesignSystem.typography.fontSize['2xl'],
  },
  pathHeaderText: {
    flex: 1,
    marginLeft: DesignSystem.spacing[4],
  },
  pathLevel: {
    fontSize: DesignSystem.typography.fontSize.sm,
    fontWeight: DesignSystem.typography.fontWeight.medium,
    color: 'rgba(255, 255, 255, 0.8)',
    fontFamily: DesignSystem.typography.fonts.primary,
    marginBottom: DesignSystem.spacing[1],
  },
  pathTitle: {
    fontSize: DesignSystem.typography.fontSize['2xl'],
    fontWeight: DesignSystem.typography.fontWeight.bold,
    color: DesignSystem.colors.text.inverse,
    fontFamily: DesignSystem.typography.fonts.heading,
  },
  pathStats: {
    alignItems: 'flex-end',
  },
  pathDuration: {
    fontSize: DesignSystem.typography.fontSize.sm,
    fontWeight: DesignSystem.typography.fontWeight.medium,
    color: 'rgba(255, 255, 255, 0.9)',
    fontFamily: DesignSystem.typography.fonts.primary,
    marginBottom: DesignSystem.spacing[1],
  },
  pathStudents: {
    fontSize: DesignSystem.typography.fontSize.xs,
    color: 'rgba(255, 255, 255, 0.7)',
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  pathCardContent: {
    padding: DesignSystem.spacing[6],
  },
  pathDescription: {
    fontSize: DesignSystem.typography.fontSize.base,
    color: DesignSystem.colors.text.secondary,
    lineHeight: DesignSystem.typography.lineHeight.relaxed,
    marginBottom: DesignSystem.spacing[6],
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  coursesList: {
    marginBottom: DesignSystem.spacing[6],
  },
  coursesTitle: {
    fontSize: DesignSystem.typography.fontSize.lg,
    fontWeight: DesignSystem.typography.fontWeight.semibold,
    color: DesignSystem.colors.text.primary,
    marginBottom: DesignSystem.spacing[4],
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  courseItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: DesignSystem.spacing[3],
    paddingLeft: DesignSystem.spacing[2],
  },
  courseBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: DesignSystem.colors.primary[600],
    marginTop: 8,
    marginRight: DesignSystem.spacing[3],
  },
  courseContent: {
    flex: 1,
  },
  courseTitle: {
    fontSize: DesignSystem.typography.fontSize.base,
    fontWeight: DesignSystem.typography.fontWeight.semibold,
    color: DesignSystem.colors.text.primary,
    marginBottom: DesignSystem.spacing[1],
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  courseBrief: {
    fontSize: DesignSystem.typography.fontSize.sm,
    color: DesignSystem.colors.text.tertiary,
    lineHeight: DesignSystem.typography.lineHeight.snug,
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  featuresContainer: {
    marginBottom: DesignSystem.spacing[6],
  },
  featuresTitle: {
    fontSize: DesignSystem.typography.fontSize.lg,
    fontWeight: DesignSystem.typography.fontWeight.semibold,
    color: DesignSystem.colors.text.primary,
    marginBottom: DesignSystem.spacing[3],
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  featuresList: {
    gap: DesignSystem.spacing[2],
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: DesignSystem.spacing[2],
  },
  featureCheck: {
    fontSize: DesignSystem.typography.fontSize.sm,
    color: DesignSystem.colors.accent.green,
    fontWeight: DesignSystem.typography.fontWeight.bold,
  },
  featureText: {
    fontSize: DesignSystem.typography.fontSize.sm,
    color: DesignSystem.colors.text.secondary,
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  pathCTA: {
    borderRadius: DesignSystem.borderRadius.full,
    paddingHorizontal: DesignSystem.spacing[8],
    paddingVertical: DesignSystem.spacing[4],
    alignItems: 'center',
    shadowColor: DesignSystem.colors.neutral[900],
    shadowOpacity: 0.15,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 4,
  },
  pathCTAText: {
    fontSize: DesignSystem.typography.fontSize.lg,
    fontWeight: DesignSystem.typography.fontWeight.semibold,
    color: DesignSystem.colors.text.inverse,
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  pathCTAHover: {
    color: DesignSystem.colors.text.inverse,
  },
});