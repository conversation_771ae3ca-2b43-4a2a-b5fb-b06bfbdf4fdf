import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { Dimensions, Image, Pressable, StyleSheet, Text, View } from 'react-native';
import { DesignSystem, isBreakpoint } from '../constants/DesignSystem';

export default function HeroSection() {
  const { width } = Dimensions.get('window');
  const isMobile = !isBreakpoint('lg', width);
  const isTablet = isBreakpoint('md', width) && !isBreakpoint('lg', width);

  return (
    <View style={styles.heroSection}>
      <LinearGradient
        colors={[DesignSystem.colors.background.accent, DesignSystem.colors.background.primary]}
        style={styles.gradientBackground}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Background Pattern */}
        <View style={styles.backgroundPattern} />

        <View style={[styles.heroContainer, isMobile && styles.heroContainerMobile]}>
          <View style={[styles.heroContent, isMobile && styles.heroContentMobile]}>
            {/* Hero Text Content */}
            <View style={[styles.heroTextSection, isMobile && styles.heroTextSectionMobile]}>
              {/* Badge */}
              <View style={styles.heroBadge}>
                <Text style={styles.heroBadgeText}>🚀 Empowering Young Entrepreneurs</Text>
              </View>

              {/* Main Headline */}
              <Text style={[styles.heroTitle, isMobile && styles.heroTitleMobile]}>
                Transform Your{'\n'}
                <Text style={styles.heroTitleAccent}>Business Ideas</Text>{'\n'}
                Into Reality
              </Text>

              {/* Subtitle */}
              <Text style={[styles.heroSubtitle, isMobile && styles.heroSubtitleMobile]}>
                Join thousands of young entrepreneurs who've launched successful businesses through our comprehensive bootcamps and courses. Start your journey today.
              </Text>

              {/* CTA Buttons */}
              <View style={[styles.ctaContainer, isMobile && styles.ctaContainerMobile]}>
                <Pressable style={[styles.primaryCTA, isMobile && styles.primaryCTAMobile]}>
                  {({ hovered }) => (
                    <Text style={[styles.primaryCTAText, hovered && styles.primaryCTAHover]}>
                      Start Your Journey
                    </Text>
                  )}
                </Pressable>
                <Pressable style={[styles.secondaryCTA, isMobile && styles.secondaryCTAMobile]}>
                  {({ hovered }) => (
                    <View style={styles.secondaryCTAContent}>
                      <View style={styles.playIcon}>
                        <Text style={styles.playIconText}>▶</Text>
                      </View>
                      <Text style={[styles.secondaryCTAText, hovered && styles.secondaryCTAHover]}>
                        Watch Success Stories
                      </Text>
                    </View>
                  )}
                </Pressable>
              </View>

              {/* Trust Indicators */}
              <View style={styles.trustIndicators}>
                <View style={styles.trustItem}>
                  <Text style={styles.trustNumber}>500+</Text>
                  <Text style={styles.trustLabel}>Students Graduated</Text>
                </View>
                <View style={styles.trustDivider} />
                <View style={styles.trustItem}>
                  <Text style={styles.trustNumber}>95%</Text>
                  <Text style={styles.trustLabel}>Success Rate</Text>
                </View>
                <View style={styles.trustDivider} />
                <View style={styles.trustItem}>
                  <Text style={styles.trustNumber}>50+</Text>
                  <Text style={styles.trustLabel}>Expert Mentors</Text>
                </View>
              </View>
            </View>
            {/* Hero Visual Section */}
            <View style={[styles.heroVisualSection, isMobile && styles.heroVisualSectionMobile]}>
              <View style={styles.heroImageContainer}>
                {/* Main Hero Image */}
                <View style={styles.heroImageWrapper}>
                  <Image
                    source={{ uri: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?auto=format&fit=crop&w=800&q=80' }}
                    style={styles.heroImage}
                    resizeMode="cover"
                  />
                  {/* Floating Cards */}
                  <View style={[styles.floatingCard, styles.floatingCard1]}>
                    <Text style={styles.floatingCardEmoji}>💡</Text>
                    <Text style={styles.floatingCardText}>Innovation</Text>
                  </View>
                  <View style={[styles.floatingCard, styles.floatingCard2]}>
                    <Text style={styles.floatingCardEmoji}>🎯</Text>
                    <Text style={styles.floatingCardText}>Success</Text>
                  </View>
                  <View style={[styles.floatingCard, styles.floatingCard3]}>
                    <Text style={styles.floatingCardEmoji}>🚀</Text>
                    <Text style={styles.floatingCardText}>Growth</Text>
                  </View>
                </View>

                {/* Decorative Elements */}
                <View style={styles.decorativeCircle1} />
                <View style={styles.decorativeCircle2} />
                <View style={styles.decorativeCircle3} />
              </View>
            </View>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  heroSection: {
    position: 'relative',
    overflow: 'hidden',
  },
  gradientBackground: {
    paddingTop: DesignSystem.spacing[24],
    paddingBottom: DesignSystem.spacing[32],
    minHeight: 700,
  },
  backgroundPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.03,
    backgroundImage: Platform.OS === 'web' ?
      'radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)' :
      undefined,
  },
  heroContainer: {
    maxWidth: 1280,
    width: '100%',
    alignSelf: 'center',
    paddingHorizontal: DesignSystem.spacing[6],
    position: 'relative',
    zIndex: 1,
  },
  heroContainerMobile: {
    paddingHorizontal: DesignSystem.spacing[4],
  },
  heroContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: DesignSystem.spacing[16],
    minHeight: 600,
  },
  heroContentMobile: {
    flexDirection: 'column',
    gap: DesignSystem.spacing[12],
    minHeight: 'auto',
  },
  heroTextSection: {
    flex: 1,
    maxWidth: 600,
  },
  heroTextSectionMobile: {
    maxWidth: '100%',
    alignItems: 'center',
    textAlign: 'center',
  },
  heroBadge: {
    backgroundColor: DesignSystem.colors.background.primary,
    borderRadius: DesignSystem.borderRadius.full,
    paddingHorizontal: DesignSystem.spacing[4],
    paddingVertical: DesignSystem.spacing[2],
    alignSelf: 'flex-start',
    marginBottom: DesignSystem.spacing[6],
    shadowColor: DesignSystem.colors.primary[600],
    shadowOpacity: 0.1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
  },
  heroBadgeText: {
    fontSize: DesignSystem.typography.fontSize.sm,
    fontWeight: DesignSystem.typography.fontWeight.semibold,
    color: DesignSystem.colors.primary[600],
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  heroTitle: {
    fontSize: DesignSystem.typography.fontSize['6xl'],
    fontWeight: DesignSystem.typography.fontWeight.black,
    color: DesignSystem.colors.text.primary,
    marginBottom: DesignSystem.spacing[6],
    lineHeight: DesignSystem.typography.lineHeight.tight,
    fontFamily: DesignSystem.typography.fonts.heading,
    letterSpacing: DesignSystem.typography.letterSpacing.tight,
  },
  heroTitleMobile: {
    fontSize: DesignSystem.typography.fontSize['4xl'],
    textAlign: 'center',
  },
  heroTitleAccent: {
    color: DesignSystem.colors.primary[600],
  },
  heroSubtitle: {
    fontSize: DesignSystem.typography.fontSize.xl,
    color: DesignSystem.colors.text.secondary,
    marginBottom: DesignSystem.spacing[8],
    lineHeight: DesignSystem.typography.lineHeight.relaxed,
    fontFamily: DesignSystem.typography.fonts.primary,
    fontWeight: DesignSystem.typography.fontWeight.normal,
    maxWidth: 540,
  },
  heroSubtitleMobile: {
    fontSize: DesignSystem.typography.fontSize.lg,
    textAlign: 'center',
    maxWidth: '100%',
  },
  ctaContainer: {
    flexDirection: 'row',
    gap: DesignSystem.spacing[4],
    marginBottom: DesignSystem.spacing[12],
    flexWrap: 'wrap',
  },
  ctaContainerMobile: {
    flexDirection: 'column',
    alignItems: 'center',
    gap: DesignSystem.spacing[3],
  },
  primaryCTA: {
    backgroundColor: DesignSystem.colors.primary[600],
    borderRadius: DesignSystem.borderRadius.full,
    paddingHorizontal: DesignSystem.spacing[8],
    paddingVertical: DesignSystem.spacing[4],
    shadowColor: DesignSystem.colors.primary[600],
    shadowOpacity: 0.3,
    shadowRadius: 12,
    shadowOffset: { width: 0, height: 4 },
    elevation: 6,
  },
  primaryCTAMobile: {
    width: '100%',
    alignItems: 'center',
  },
  primaryCTAText: {
    fontSize: DesignSystem.typography.fontSize.lg,
    fontWeight: DesignSystem.typography.fontWeight.semibold,
    color: DesignSystem.colors.text.inverse,
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  primaryCTAHover: {
    color: DesignSystem.colors.text.inverse,
  },
  secondaryCTA: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: DesignSystem.spacing[6],
    paddingVertical: DesignSystem.spacing[4],
    borderRadius: DesignSystem.borderRadius.full,
    backgroundColor: 'transparent',
  },
  secondaryCTAMobile: {
    width: '100%',
    justifyContent: 'center',
  },
  secondaryCTAContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: DesignSystem.spacing[3],
  },
  playIcon: {
    width: 48,
    height: 48,
    borderRadius: DesignSystem.borderRadius.full,
    backgroundColor: DesignSystem.colors.background.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: DesignSystem.colors.neutral[900],
    shadowOpacity: 0.1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 4,
  },
  playIconText: {
    fontSize: DesignSystem.typography.fontSize.base,
    color: DesignSystem.colors.primary[600],
    marginLeft: 2,
  },
  secondaryCTAText: {
    fontSize: DesignSystem.typography.fontSize.lg,
    fontWeight: DesignSystem.typography.fontWeight.semibold,
    color: DesignSystem.colors.text.primary,
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  secondaryCTAHover: {
    color: DesignSystem.colors.primary[600],
  },
  trustIndicators: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: DesignSystem.spacing[6],
    paddingTop: DesignSystem.spacing[6],
    borderTopWidth: 1,
    borderTopColor: DesignSystem.colors.neutral[200],
  },
  trustItem: {
    alignItems: 'center',
  },
  trustNumber: {
    fontSize: DesignSystem.typography.fontSize['2xl'],
    fontWeight: DesignSystem.typography.fontWeight.bold,
    color: DesignSystem.colors.primary[600],
    fontFamily: DesignSystem.typography.fonts.heading,
    marginBottom: DesignSystem.spacing[1],
  },
  trustLabel: {
    fontSize: DesignSystem.typography.fontSize.sm,
    color: DesignSystem.colors.text.tertiary,
    fontFamily: DesignSystem.typography.fonts.primary,
    textAlign: 'center',
  },
  trustDivider: {
    width: 1,
    height: 32,
    backgroundColor: DesignSystem.colors.neutral[300],
  },
  heroVisualSection: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  heroVisualSectionMobile: {
    marginTop: DesignSystem.spacing[8],
  },
  heroImageContainer: {
    position: 'relative',
    width: 500,
    height: 500,
  },
  heroImageWrapper: {
    width: 400,
    height: 400,
    borderRadius: DesignSystem.borderRadius['3xl'],
    overflow: 'hidden',
    backgroundColor: DesignSystem.colors.background.primary,
    shadowColor: DesignSystem.colors.neutral[900],
    shadowOpacity: 0.15,
    shadowRadius: 24,
    shadowOffset: { width: 0, height: 8 },
    elevation: 12,
    position: 'relative',
    zIndex: 2,
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  floatingCard: {
    position: 'absolute',
    backgroundColor: DesignSystem.colors.background.primary,
    borderRadius: DesignSystem.borderRadius.xl,
    padding: DesignSystem.spacing[3],
    shadowColor: DesignSystem.colors.neutral[900],
    shadowOpacity: 0.1,
    shadowRadius: 12,
    shadowOffset: { width: 0, height: 4 },
    elevation: 6,
    flexDirection: 'row',
    alignItems: 'center',
    gap: DesignSystem.spacing[2],
    zIndex: 3,
  },
  floatingCard1: {
    top: 60,
    left: -20,
  },
  floatingCard2: {
    top: 200,
    right: -30,
  },
  floatingCard3: {
    bottom: 80,
    left: -10,
  },
  floatingCardEmoji: {
    fontSize: DesignSystem.typography.fontSize.lg,
  },
  floatingCardText: {
    fontSize: DesignSystem.typography.fontSize.sm,
    fontWeight: DesignSystem.typography.fontWeight.semibold,
    color: DesignSystem.colors.text.primary,
    fontFamily: DesignSystem.typography.fonts.primary,
  },
  decorativeCircle1: {
    position: 'absolute',
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: DesignSystem.colors.primary[100],
    top: -20,
    right: -20,
    zIndex: 1,
  },
  decorativeCircle2: {
    position: 'absolute',
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: DesignSystem.colors.secondary[100],
    bottom: -10,
    right: 60,
    zIndex: 1,
  },
  decorativeCircle3: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: DesignSystem.colors.accent.orange + '20',
    top: 100,
    left: -30,
    zIndex: 1,
  },
});