import React from 'react';
import { Dimensions, Image, Platform, Pressable, StyleSheet, Text, View } from 'react-native';

const windowWidth = Dimensions.get('window').width;
const isMobile = windowWidth < 700;

export default function HeroSection() {
  return (
    <View style={styles.heroBg}>
      {/* Background Accent (gradient shape) */}
      <View style={styles.gradientAccent} />
      <View style={[styles.heroContainer, isMobile && { flexDirection: 'column' }]}> 
        <View style={styles.heroTextCol}>
          <Text style={[styles.heroTitle, isMobile && { textAlign: 'center' }]}>Empower Your Entrepreneurial Journey</Text>
          <Text style={[styles.heroSubtitle, isMobile && { textAlign: 'center' }]}>
            Learn, grow, and connect with Imuka Juniors. Join our bootcamps and courses to unlock your business potential.
          </Text>
          <View style={styles.ctaRow}>
            <Pressable style={[styles.ctaButton, isMobile && { alignSelf: 'center' }]}> 
              <Text style={styles.ctaButtonText}>Get Started</Text>
            </Pressable>
            <Pressable style={[styles.ctaButtonSecondary, isMobile && { alignSelf: 'center' }]}> 
              <Text style={styles.ctaButtonSecondaryText}>Browse Bootcamps</Text>
            </Pressable>
          </View>
        </View>
        <View style={styles.heroImageCol}>
          {Platform.OS === 'web' ? (
            <View style={[styles.webImageWrapper, isMobile ? { width: 180, height: 200 } : { width: 400, height: 440 }]}> 
              <Image
                source={{ uri: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=600&q=80' }}
                style={[styles.heroImage, { width: '100%', height: '100%' }]}
                resizeMode="cover"
              />
              {/* Wavy SVG overlay for web, slightly overlapping the image */}
              <svg
                width={isMobile ? 180 : 400}
                height={isMobile ? 60 : 100}
                viewBox="0 0 400 100"
                style={{ position: 'absolute', left: 0, right: 0, bottom: -1, zIndex: 2 }}
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M0,0 C80,80 320,80 400,0 L400,100 L0,100 Z"
                  fill="#EEF2FF"
                />
              </svg>
            </View>
          ) : (
            // Dynamically require the native SVG image component
            (() => {
              const HeroImageNative = require('./HeroImageNative').default;
              return <HeroImageNative isMobile={isMobile} />;
            })()
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  heroBg: {
    backgroundColor: '#EEF2FF',
    paddingTop: 96,
    paddingBottom: 96,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    position: 'relative',
    overflow: 'hidden',
  },
  gradientAccent: {
    position: 'absolute',
    top: -120,
    left: -120,
    width: 400,
    height: 400,
    borderRadius: 200,
    backgroundColor: 'rgba(99,102,241,0.12)',
    zIndex: 0,
  },
  heroContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    maxWidth: 1200,
    width: '100%',
    alignSelf: 'center',
    gap: 48,
    paddingHorizontal: 24,
    position: 'relative',
    zIndex: 1,
  },
  heroTextCol: {
    flex: 1,
    minWidth: 280,
    maxWidth: 600,
  },
  heroTitle: {
    fontSize: 44,
    fontWeight: '900',
    color: '#1D4ED8',
    marginBottom: 20,
    letterSpacing: 1.2,
    fontFamily: 'System',
    textAlign: 'left',
  },
  heroSubtitle: {
    fontSize: 20,
    color: '#374151',
    marginBottom: 36,
    fontFamily: 'System',
    lineHeight: 30,
    textAlign: 'left',
    maxWidth: 500,
  },
  ctaRow: {
    flexDirection: 'row',
    gap: 16,
    flexWrap: 'wrap',
  },
  ctaButton: {
    backgroundColor: '#1D4ED8',
    borderRadius: 999,
    paddingHorizontal: 40,
    paddingVertical: 16,
    alignSelf: 'flex-start',
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  ctaButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 18,
    letterSpacing: 1.2,
    textTransform: 'uppercase',
    fontFamily: 'System',
  },
  ctaButtonSecondary: {
    backgroundColor: '#fff',
    borderRadius: 999,
    paddingHorizontal: 32,
    paddingVertical: 16,
    alignSelf: 'flex-start',
    borderWidth: 2,
    borderColor: '#1D4ED8',
    marginLeft: 0,
  },
  ctaButtonSecondaryText: {
    color: '#1D4ED8',
    fontWeight: 'bold',
    fontSize: 18,
    letterSpacing: 1.2,
    textTransform: 'uppercase',
    fontFamily: 'System',
  },
  heroImageCol: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 200,
    maxWidth: 400,
    marginTop: isMobile ? 32 : 0,
  },
  heroImage: {
    width: 320,
    height: 320,
    borderRadius: 32,
    backgroundColor: '#fff',
    objectFit: 'cover',
  },
  webImageWrapper: {
    position: 'relative',
    overflow: 'hidden',
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    backgroundColor: '#fff',
  },
  diagonalOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: '40%',
    backgroundColor: '#EEF2FF', // match heroBg
    transform: [{ skewY: '-12deg' }],
    zIndex: 2,
    borderBottomLeftRadius: 32,
    borderBottomRightRadius: 32,
  },
}); 