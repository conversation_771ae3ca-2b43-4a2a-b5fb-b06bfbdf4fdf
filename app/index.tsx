import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import AboutImukaJuniors from '../components/AboutImukaJuniors';
import BootcampsSection from '../components/BootcampsSection';
import CareersSection from '../components/CareersSection';
import EntrepreneurialCoursesSection from '../components/EntrepreneurialCoursesSection';
import FooterSection from '../components/FooterSection';
import HeroSection from '../components/HeroSection';
import NavigationBar from '../components/NavigationBar';

const sectionBackgrounds = [
  '#fff',        // HeroSection (handled in component)
  '#F9FAFB',     // ThreeSlider
  '#fff',        // About
  '#F9FAFB',     // Course Levels
  '#fff',        // Bootcamps
];

function Section({ children, index }: { children: React.ReactNode; index: number }) {
  const background = sectionBackgrounds[index % sectionBackgrounds.length];
  return (
    <View style={[styles.section, { backgroundColor: background }]}> {children} </View>
  );
}

export default function LandingPage() {
  return (
    <ScrollView style={styles.container}>
      <NavigationBar />
      <HeroSection />
      <EntrepreneurialCoursesSection />
      <Section index={0}>
        <AboutImukaJuniors />
      </Section>
      <Section index={1}>
        <CareersSection />
      </Section>
      <Section index={2}>
        <BootcampsSection />
      </Section>
      <FooterSection />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff' },
  section: {
    width: '100%',
    alignSelf: 'center',
    paddingVertical: 64,
    borderBottomWidth: 0,
    borderBottomColor: 'transparent',
  },
}); 