import { ScrollView, Text, View } from 'react-native';

export default function LandingPage() {
  return (
    <ScrollView className="flex-1 bg-white dark:bg-black px-4 py-8">
      {/* Header */}
      <View className="mb-8 items-center">
        <Text className="text-3xl font-bold text-blue-700 dark:text-blue-300 mb-2">Imuka Juniors</Text>
        <Text className="text-base text-gray-700 dark:text-gray-300 text-center max-w-md">
          Empowering young entrepreneurs through boot camps and courses. Join us to discover, scale, and grow your business journey!
        </Text>
      </View>

      {/* About Section */}
      <View className="mb-8">
        <Text className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">About Imuka Juniors</Text>
        <Text className="text-gray-700 dark:text-gray-300">
          Imuka Juniors is a platform designed to nurture and empower the next generation of business leaders. We offer structured learning paths, hands-on boot camps, and a supportive community to help you succeed.
        </Text>
      </View>

      {/* Levels Section */}
      <View className="mb-8">
        <Text className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Our Levels</Text>
        <View className="flex-row justify-between gap-2">
          <View className="flex-1 bg-blue-100 dark:bg-blue-900 rounded-lg p-4 items-center">
            <Text className="font-bold text-blue-800 dark:text-blue-200 mb-1">Discover</Text>
            <Text className="text-xs text-gray-600 dark:text-gray-300 text-center">Start your journey and explore business basics.</Text>
          </View>
          <View className="flex-1 bg-green-100 dark:bg-green-900 rounded-lg p-4 items-center">
            <Text className="font-bold text-green-800 dark:text-green-200 mb-1">Scale</Text>
            <Text className="text-xs text-gray-600 dark:text-gray-300 text-center">Grow your skills and take your business to the next level.</Text>
          </View>
          <View className="flex-1 bg-yellow-100 dark:bg-yellow-900 rounded-lg p-4 items-center">
            <Text className="font-bold text-yellow-800 dark:text-yellow-200 mb-1">Grow</Text>
            <Text className="text-xs text-gray-600 dark:text-gray-300 text-center">Master advanced concepts and expand your impact.</Text>
          </View>
        </View>
      </View>

      {/* Upcoming Boot Camps Section */}
      <View className="mb-8">
        <Text className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Upcoming Boot Camps</Text>
        <View className="space-y-4">
          {/* Placeholder boot camp cards */}
          <View className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow border border-gray-200 dark:border-gray-700">
            <Text className="font-bold text-lg text-blue-700 dark:text-blue-300 mb-1">Entrepreneurship 101</Text>
            <Text className="text-gray-600 dark:text-gray-300 mb-1">Starts: July 15, 2024</Text>
            <Text className="text-xs text-gray-500 dark:text-gray-400">Learn the basics of starting and running a business.</Text>
          </View>
          <View className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow border border-gray-200 dark:border-gray-700">
            <Text className="font-bold text-lg text-blue-700 dark:text-blue-300 mb-1">Digital Marketing Boot Camp</Text>
            <Text className="text-gray-600 dark:text-gray-300 mb-1">Starts: August 5, 2024</Text>
            <Text className="text-xs text-gray-500 dark:text-gray-400">Master online marketing strategies for your business.</Text>
          </View>
        </View>
      </View>

      {/* Call to Action */}
      <View className="items-center mt-8">
        <Text className="text-base text-gray-700 dark:text-gray-300 mb-2">Ready to get started?</Text>
        <View className="flex-row gap-4">
          <View className="bg-blue-700 px-6 py-2 rounded-full">
            <Text className="text-white font-semibold">Register</Text>
          </View>
          <View className="bg-gray-200 dark:bg-gray-700 px-6 py-2 rounded-full">
            <Text className="text-blue-700 dark:text-blue-300 font-semibold">Login</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}
